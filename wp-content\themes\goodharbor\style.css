@charset "UTF-8"; /*
Theme Name:Good Harbor CRE Wordpress Theme
Author:Focused CRE
Author URI:https://focusedcre.com
Description:Custom wordpress theme for Good Harbor CRE.
Version:1
*/:root{--primary:#feb23d; --secondary:#2d4b68;}
html,body{overflow-x:hidden; width:100%;}
body{font-family:'Roboto',sans-serif;}
#mainWebContainer{overflow-x:hidden; position:relative;}
/************************* fonts,buttons,icons and text blocks styles**********************************/





h1{font-size:50px; color:#000000; line-height:50px;}
h2{font-size:44px; color:#000000; line-height:44px;}
h3{font-size:24px; color:#000000; line-height:26px; font-family:'open-sans',sans-serif;}
h4{font-size:20px; color:#000000; line-height:30px;}
h5{font-size:16px; color:#000000; line-height:24px;}
h6{font-size:14px; color:#000000; line-height:18px;}
.heading-inline{display:inline !important;}
a{color:#000000; font-weight:400; text-decoration:none; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
a:hover{color:#000000; text-decoration:none;}
a:focus{text-decoration:none; outline:none}
ul{margin:0; padding:0}
ul li{list-style:none;}
img{image-rendering:-webkit-optimize-contrast;}
#map{height:400px;}
/*--------------------------------------------------------------
# Top Bar
--------------------------------------------------------------*/





#topbar{background:#306178; height:40px; font-size:14px; border-bottom:1px solid #b49e3f; transition:all 0.5s;}
#topbar.topbar-scrolled{top:-40px;}
#topbar .contact-info a{color:#ffffff; font-size:14px; line-height:14px; font-weight:500;}
#topbar .contact-info a:hover{color:#ffffff;}
#topbar .contact-info a i{color:#ffffff;}
.cusBtn{position:relative; padding:14px 30px; font-weight:500; display:inline-block; text-transform:uppercase; font-size:16px; line-height:20px; color:#fff; border:1px solid #ffffff;}
.cusBtn:hover{color:#fff;}
.pageMargin{margin-top:120px;}
/*--------------------------------------------------------------
# Header 
--------------------------------------------------------------*/





#header{background-color:#ffffff; transition:all 0.5s; padding-block:22px;}
#header.header-scrolled{background:var(--primary); top:0;}
#header .logo img{width:250px;}
#header.header-scrolled .navbar li a,.navbar li a:focus{color:#ffffff;}
#header .navbar li.current-menu-item.menu-item-object-page a:after,.navbar li a:hover:after{background:var(--primary);}
#header.header-scrolled .navbar li.current-menu-item a:after{background:#ffffff;}
#header.header-scrolled .logo img{filter:invert(100%);}
#header.header-scrolled .navbar li a:hover:after{background:#ffffff;}
/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/





/**
* Desktop Navigation
*/






/*--------------------------------------------------------------
# Desktop Navigation
--------------------------------------------------------------*/





@media (min-width:1024px){
.navbar{padding:0;}
.navbar ul{margin:0; padding:0; display:flex; list-style:none; align-items:center; gap:20px;}
.navbar li{position:relative; padding:0 15px 0 15px;}
    .navbar li a, .navbar li a:focus {
        position: relative;
   color: var(--secondary);
        white-space: nowrap;
        transition: 0.3s;
        text-transform: uppercase;
        font-size: 15px;
        line-height: 17px;
        font-weight: 600;
    }
.navbar li a:hover,.navbar li.current-menu-item a,.navbar li.current-menu-item:focus a,.navbar li:hover a{color:var(--primary);}
    .navbar li a:after {
        position: absolute;
        content: '';
		top: 50%;
		transform: translateY(-50%);
        height: 0;
        width: 2px;
        left: -6px;      
        background: var(--primary);
        -webkit-transition: height 0.3s ease;
        -moz-transition: height 0.3s ease;
        -ms-transition: height 0.3s ease;
        -o-transition: height 0.3s ease;
        transition: height 0.3s ease;
    }
.navbar li.current-menu-item a:after,.navbar li a:hover:after{height: 100%;}
/* .navbar li.menu-item-has-children a:after{content:"\f078"; display:inline-block; font-family:'fontawesome'; position:absolute; top:50%; right:0; transform:translateY(-50%); font-size:12px;} */
.navbar li.menu-item-has-children ul.sub-menu{display:block; position:absolute; left:28px; top:calc(100% + 30px); margin:0; padding:10px 0; z-index:99; opacity:0; visibility:hidden; background:#fff; box-shadow:0 0 30px rgba(127,137,161,0.25); transition:0.3s; border-radius:4px;}
.navbar li.menu-item-has-children ul.sub-menu li{min-width:200px;}
.navbar li.menu-item-has-children ul.sub-menu a{padding:10px 20px; font-size:15px; text-transform:none; font-weight:600; color:#082744;}
.navbar li.menu-item-has-children ul.sub-menu a i{font-size:12px;}
.navbar li.menu-item-has-children ul.sub-menu a:hover,.navbar li.menu-item-has-children ul.sub-menu li:hover>a{color:#000000;}
}
@media (min-width:1024px) and (max-width:1366px){
.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{left:-90%;}
.navbar .menu-item-has-children .menu-item-has-children:hover>ul.sub-menu{left:-100%;}
}
@media (min-width:1024px){
.mobile-nav-show,.mobile-nav-hide{display:none;}
}
/*--------------------------------------------------------------
# Mobile Navigation
--------------------------------------------------------------*/





@media (max-width:1023px){
#header{padding:15px 0;}
#header .logo img{width:200px;}
.navbar{position:fixed; top:0; right:-100%; width:100%; max-width:400px; bottom:0; transition:0.3s; z-index:9997;}
.navbar ul{position:absolute; inset:0; padding:50px 0 10px 0; margin:0; background:#000; opacity:.95; overflow-y:auto; transition:0.3s; z-index:9998;}
.navbar a,.navbar a:focus{display:flex; align-items:center; justify-content:space-between; padding:10px 20px; font-size:15px; font-weight:600; color:rgba(255,255,255,0.7); white-space:nowrap; transition:0.3s;}
.navbar a i,.navbar a:focus i{font-size:12px; line-height:0; margin-left:5px;}
.navbar a:hover,.navbar .current-menu-item a,.navbar .current-menu-item:focus a,.navbar li:hover>a{color:#fff;}
.navbar .menu-item-has-children ul.sub-menu,.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{position:static; display:none; padding:10px 0; margin:10px 20px; background-color:rgba(20,35,51,0.6);}
.navbar .menu-item-has-children>.submenu-active,.navbar .menu-item-has-children .menu-item-has-children>.submenu-active{display:block;}
.mobile-nav-show{font-size:28px; cursor:pointer; line-height:0; transition:0.5s; color:#000; padding-right:30px;}
.mobile-nav-hide{color:rgba(255,255,255,0.9); font-size:28px; cursor:pointer; line-height:0; transition:0.5s; position:fixed; right:15px; top:25px; z-index:9999;}
.mobile-nav-active{overflow:hidden;}
.mobile-nav-active .navbar{right:0;}
.mobile-nav-active .navbar:before{content:""; position:fixed; inset:0; background:#000000; opacity:.7; z-index:9996;}
}
@media (min-width:1023px){
.mobile-nav-show,.mobile-nav-hide{display:none !important;}
}
.sub-menu-toggle{display:none !important;}
.id-scrool-fix{position:relative; top:-100px;}
/*--------------------------------------------------------------
# Home Slider
--------------------------------------------------------------*/





.homeSlider{position:relative;}
.sliderInner{width:100%; height:100vh; position:relative; background-attachment:fixed !important;}
.sliderInner:after{content:''; position:absolute; top:0; left:0; width:100%; height:100%; mix-blend-mode:multiply; background:rgb(61 143 176 / 100%); display:block;}
.sliderInner .sliderInnerText{margin-top:100px; position:relative; z-index:1; text-align:left; max-width:100%;}
.sliderInner .sliderInnerText h1{color:#ffffff; margin:0; font-size:42px; line-height:46px; font-weight:700;}
.sliderInner .sliderInnerText h2{font-size:32px; line-height:36px; font-weight:400; margin-bottom:28px; color:#ffffff;}
.sliderInner .sliderInnerText p{color:#fff; font-size:18px;}
.sliderInner .sliderInnerText p a{color:var(--primary); font-size:18px; font-weight:700;}
.homeSlider .owl-dots{position:absolute; bottom:90px; left:51%; display:flex; gap:12px; transform:translate(-50%,0); padding-left:0; max-width:100%; width:1140px;}
.homeSlider button.owl-dot span{width:12px; height:12px; border-radius:50px; display:inline-block; background:transparent; border:1px solid #ffffff;}
.homeSlider button.owl-dot.active span{background:#f9b241; border:1px solid #f9b241;}
/*--------------------------------------------------------------
# Home Slider
--------------------------------------------------------------*/



/*--------------------------------------------------------------
# Home About Us
--------------------------------------------------------------*/




.aboutUs{padding:80px 0 60px; position:relative; z-index:1;}
.aboutUs::before{content:''; position:absolute; top:0; left:0; width:100%; height:100%; background:rgb(61 143 176 / 85%); z-index:-1;}
.aboutUs h2{color:var(--primary); text-transform:uppercase; font-size:32px; line-height:36px; font-weight:700; border-bottom:1px solid #ffffff; padding-bottom:10px; margin-bottom:28px;}
.aboutUs p{color:#ffffff; font-size:17px; font-weight:400;}
.aboutUs p a{color:#ffffff; text-decoration:underline;}
.aboutUs p strong{color:#ffffff; font-weight:700; display:block;}
.aboutBtns{display:flex; gap:2rem; justify-content:space-between; margin-top:60px; text-align:center;}
.aboutBtns a{flex:1;}
.aboutBtns a:nth-child(1){background:var(--primary); border:1px solid var(--primary); color:#224b6a;}
.aboutBtns a:nth-child(2){background:#fe7d2d; border:1px solid #fe7d2d;}
.aboutBtns a:nth-child(3){background:#224b6a; border:1px solid #224b6a;}
/*--------------------------------------------------------------
# Home About Us
--------------------------------------------------------------*/




/*--------------------------------------------------------------
# Home Counter
--------------------------------------------------------------*/


.counterStats{background:var(--secondary);}
.counterStats .counterStatsInner{flex-wrap:wrap;}
.counterStats .counterStatsInner .counterBox{flex:25%; text-align:center;}
.counterStats .counterStatsInner .counterBox h3{color:transparent; /* Fill white */


	font-size:60px; line-height:60px; -webkit-text-stroke:1px #ffffff; /* Stroke white */


	font-weight:600; position:relative; margin-bottom:20px; padding-bottom:20px; transition:all 0.3s; text-transform:uppercase;}
.counterStats .counterStatsInner .counterBox h3:hover{color:#0091b3; /* Fill */


	-webkit-text-stroke:1px #0091b3; /* Stroke */}
.counterStats .counterStatsInner .counterBox h3:before{position:absolute; content:''; width:50px; height:1px; background:#ffffff; bottom:0; left:50%; transform:translate(-50%,-50%); transition:all 0.3s;}
.counterStats .counterStatsInner .counterBox:nth-of-type(1) h3{color:#0091b3; -webkit-text-stroke-width:1px; -webkit-text-stroke-color:#0091b3; font-weight:600;}
.counterStats .counterStatsInner .counterBox p{color:#ffffff; text-align:center; font-size:14px; line-height:22px; margin-bottom:0; text-transform:uppercase;}
/*--------------------------------------------------------------
# Home Counter
--------------------------------------------------------------*/



/*--------------------------------------------------------------
# Home Testimonials CPO
--------------------------------------------------------------*/



.testimonialsCpo{padding:80px 0; background:#e1e1e1;}
.testimonialsCpo h2{color:#c3412c; text-transform:uppercase; font-size:32px; line-height:36px; font-weight:700; border-bottom:1px solid #224b6a; padding-bottom:10px; margin-bottom:28px; display:inline-block;}
.testimonialInner{width:70%; margin:auto; text-align:center; max-width:100%;}
.testimonialInner p{font-size:17px; line-height:26px; color:#c3412c; margin-bottom:20px; font-weight:400;}
.testimonialInner div{margin-top:30px;}
.testimonialInner div h4{font-size:16px; line-height:22px; color:#c3412c; margin-bottom:0; font-weight:400;}
.testimonialInner div h4 strong{font-weight:600;}
.testimonialsCpo .owl-nav .owl-prev,.testimonialsCpo .owl-nav .owl-next{position:absolute; top:50%; transform:translateY(-50%); color:#00a9e3 !important; font-size:42px !important;}
.testimonialsCpo .owl-nav .owl-prev{left:-100px;}
.testimonialsCpo .owl-nav .owl-next{right:-100px;}
/*--------------------------------------------------------------
# Home Testimonials CPO
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Expand Your CRE
--------------------------------------------------------------*/


.expandYourCre{padding:80px 0; position:relative; z-index:1; text-align:center;}
.expandYourCre::before{content:''; position:absolute; top:0; left:0; width:100%; height:100%; background:rgb(61 143 176 / 85%); z-index:-1; display:block;}
.expandYourCre h2{color:#ffffff; text-transform:capitalize; font-size:36px; line-height:40px; font-weight:700; margin-bottom:28px;}
.expandYourCre a{position:relative; padding:14px 50px; font-weight:500; display:inline-block; text-transform:uppercase; font-size:17px; line-height:20px; background:#feb23d; color:#fff; border:1px solid #ffffff;}
/*--------------------------------------------------------------
# Home Expand Your CRE
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Testimonials CIP
--------------------------------------------------------------*/

.testimonialsCip{padding:80px 0; }
.testimonialsCip h2{color:var(--secondary); text-transform:uppercase; font-size:32px; line-height:36px; font-weight:700; border-bottom:1px solid var(--secondary); padding-bottom:10px; margin-bottom:28px; display:inline-block;}
.testimonialsCipInner{width:70%; margin:auto; text-align:center; max-width:100%;}
.testimonialsCipInner p{font-size:17px; line-height:26px; color:var(--secondary); margin-bottom:20px; font-weight:400;}
.testimonialsCipInner div{margin-top:30px;}
.testimonialsCipInner div h4{font-size:16px; line-height:22px; color:var(--secondary); margin-bottom:0; font-weight:400;}
.testimonialsCipInner div h4 strong{font-weight:600;}
.testimonialsCip .owl-nav .owl-prev,.testimonialsCip .owl-nav .owl-next{position:absolute; top:50%; transform:translateY(-50%); color:#00a9e3 !important; font-size:42px !important;}
.testimonialsCip .owl-nav .owl-prev{left:-100px;}
.testimonialsCip .owl-nav .owl-next{right:-100px;}
/*--------------------------------------------------------------
# Home Testimonials CIP
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Expand Your CRE
--------------------------------------------------------------*/


.expandYourCre{padding:80px 0; position:relative; z-index:1; text-align:center;}
.expandYourCre::before{content:''; position:absolute; top:0; left:0; width:100%; height:100%; background:rgb(61 143 176 / 85%); z-index:-1; display:block;}
.expandYourCre h2{color:#ffffff; text-transform:capitalize; font-size:36px; line-height:40px; font-weight:700; margin-bottom:28px;}
.expandYourCre a{position:relative; padding:16px 50px; font-weight:500; display:inline-block; text-transform:uppercase; font-size:17px; line-height:20px; background:#feb23d; color:#fff; border:1px solid #ffffff;}
/*--------------------------------------------------------------
# Home Expand Your CRE
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Home Real Estate Investing Made Simple, Passive and Profitable
--------------------------------------------------------------*/


.realEstateInvesting{padding:80px 0; position:relative; z-index:1; text-align:center; background-color: #aeaeae;}
.realEstateInvesting h2{color:#ffffff; text-transform:capitalize; font-size:36px; line-height:40px; font-weight:700; margin-bottom:28px;}
.realEstateInvesting a{position:relative; padding:16px 50px; font-weight:500; display:inline-block; text-transform:uppercase; font-size:17px; line-height:20px; background:#fe7d2d; color:#fff; border:1px solid #ffffff;}
/*--------------------------------------------------------------
# Home Real Estate Investing Made Simple, Passive and Profitable
--------------------------------------------------------------*/



/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.footer{position: relative;z-index: 1;

}

.footer::before{content:''; position:absolute; top:0; left:0; width:100%; height:100%; mix-blend-mode: multiply; background:rgba(33, 74, 107 , 1); z-index:-1; display:block;}

.footer .footerCol1 img{width: 300px;}
.footerCol1 ul{display: flex; gap: 20px;margin: 30px 0;}
.footerCol1 ul li a{color: #fff; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 2px solid #fff; border-radius: 50%;font-size: 15px;}
.footerCol1 ul li a:hover{transform: rotate(-360deg) scale(1.05); transition: all 0.3s; background: var(--primary); border: 2px solid var(--primary);}
.footer h4{color:var(--primary);text-transform: uppercase;} 
.footer .footerCol2 ul li a{color: #fff;    font-size: 15px;line-height: 25px;}


.footerCol3 ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerCol3 ul li {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 12px;
  line-height: 22px;
}

.footerCol3 ul li i {
  font-size: 16px;
  color: #fff;
  min-width: 18px;
  margin-top: 3px;
}

.footerCol3 ul li a,
.footerCol3 ul li span {
  font-size: 15px;
  color: #fff;
  text-decoration: none;
}


 
.pt-6{padding-top:5rem !important;}
.pb-6{padding-bottom:5rem !important;}
.py-6{padding-top:5rem !important; padding-bottom:5rem !important;}
.py-7{padding-top:7rem !important; padding-bottom:7rem !important;}
.form-fields, .form-fields2, .form-fields3 {
    width: 100% !important;
    box-sizing: border-box;
    padding: 10px 14px;
    font-size: 15px;
    margin-bottom: 15px;
    background: none;
    color: #ffffff;
    border: 1px solid #ffffff;
    text-transform: capitalize;
    -webkit-transition: 0.3s 
ease-in-out !important;
    -moz-transition: 0.3s ease-in-out !important;
    -ms-transition: 0.3s ease-in-out !important;
    -o-transition: 0.3s ease-in-out !important;
    transition: 0.3s 
ease-in-out !important;
}
.form-fields:focus,.form-fields2:focus,.form-fields3:focus{border:none; border-bottom:2px solid #ffffff; outline:none !important;}
.form-fields3{height:130px;}
.wpcf7-submit, .ln-widgetBox.search .searchButton a {
   
    position: relative;
    padding: 12px 30px;
    font-weight: 700;
    display: inline-block;
    text-transform: uppercase;
    border: 1px solid #c3412c;
    background: #c3412c;
    color: #ffffff;
    font-size: 14px;
}
.wpcf7-submit:hover{border:1px solid #000000; background:#000000; color:#ffffff;}
div.wpcf7 img.ajax-loader{float:left;}
.wpcf7-list-item{display:inline-block; margin-right:10px;}
div.wpcf7-response-output{float:left;}
.wpcf7-not-valid-tip{display:none !important;}
.wpcf7-not-valid{border-bottom:2px solid red !important; border:none;}
::placeholder{font-size:14px; text-transform:uppercase; color:#ffffff;}
.wpcf7 form.invalid .wpcf7-response-output,.wpcf7 form.unaccepted .wpcf7-response-output,.wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#ffffff;}
.modal-header h1{text-transform:uppercase; font-weight:700;}
.modal-body .form-fields,.modal-body .form-fields2,.modal-body .form-fields3{border-bottom:1px solid #cccccc; color:#000000; font-weight:500;}
.modal-body::placeholder{color:#000000 !important; font-weight:500; font-size:12px;}
.modal-body .wpcf7 form.invalid .wpcf7-response-output,.modal-body .wpcf7 form.unaccepted .wpcf7-response-output,.modal-body .wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#000000;}
@media (min-width:1400px){
.container,.container-lg,.container-md,.container-sm,.container-xl,.container-xxl{max-width:1140px;}
}
@media (max-width:991px){
}
@media (max-width:768px){
}
@media (max-width:481px){
}
@media (max-width:320px){
}
@media (min-width:1280px) and (max-width:1366px){
}